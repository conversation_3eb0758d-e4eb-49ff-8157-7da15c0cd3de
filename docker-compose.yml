version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: feishu-wiki-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/database.sqlite
    volumes:
      - ./backend/data:/app/data
      - ./shared:/app/shared
    depends_on:
      - db-init
    networks:
      - feishu-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/auth/app-id"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend-rsbuild
      dockerfile: Dockerfile
    container_name: feishu-wiki-frontend
    ports:
      - "3333:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:3001
    volumes:
      - ./shared:/app/shared
    depends_on:
      - backend
    networks:
      - feishu-wiki-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据库初始化服务（确保数据目录存在）
  db-init:
    image: alpine:latest
    container_name: feishu-wiki-db-init
    command: sh -c "mkdir -p /data && chmod 755 /data"
    volumes:
      - ./backend/data:/data
    networks:
      - feishu-wiki-network

  # Nginx反向代理（可选，用于生产环境）
  nginx:
    image: nginx:alpine
    container_name: feishu-wiki-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - feishu-wiki-network
    restart: unless-stopped
    profiles:
      - production

networks:
  feishu-wiki-network:
    driver: bridge

volumes:
  backend-data:
    driver: local
