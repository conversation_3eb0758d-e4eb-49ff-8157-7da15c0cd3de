# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖（SQLite需要）
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    sqlite \
    curl

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 复制共享类型定义
COPY ../shared ./shared

# 构建应用
RUN npm run build

# 创建数据目录
RUN mkdir -p /app/data

# 设置环境变量
ENV NODE_ENV=production
ENV DATABASE_PATH=/app/data/database.sqlite

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/auth/app-id || exit 1

# 启动应用
CMD ["npm", "run", "start:prod"]
