import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { Task } from './entities/task.entity';
import { TaskItem } from './entities/task-item.entity';
import { OperationLog } from './entities/operation-log.entity';

import { TasksModule } from './modules/tasks/tasks.module';
import { AuthModule } from './modules/auth/auth.module';
import { WikiModule } from './modules/wiki/wiki.module';
import { UsersModule } from './modules/users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: 'database.sqlite',
      entities: [Task, TaskItem, OperationLog],
      synchronize: true, // 生产环境应该设为false
      logging: true,
    }),
    TasksModule,
    AuthModule,
    WikiModule,
    UsersModule,
  ],
})
export class AppModule {}
