import { Injectable, Logger, OnM<PERSON>uleInit, OnModuleDestroy } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subject, Subscription, EMPTY, of } from 'rxjs';
import { concatMap, delay, catchError, retry, tap } from 'rxjs/operators';

import { Task, TaskStatus } from '../entities/task.entity';
import { TaskItem, TaskItemStatus } from '../entities/task-item.entity';
import { OperationLog, OperationType } from '../entities/operation-log.entity';
import { TasksGateway } from '../gateways/tasks.gateway';
import { WikiService } from '../modules/wiki/wiki.service';
import { TokenStorageService } from '../modules/auth/token-storage.service';

interface TaskJob {
  id: string;
  type: string;
  data: { taskId: number };
  attempts: number;
  maxAttempts: number;
}

@Injectable()
export class TaskManagerService implements OnModuleInit, OnModuleD<PERSON>roy {
  private readonly logger = new Logger(TaskManagerService.name);
  private taskSubject = new Subject<TaskJob>();
  private taskSubscription: Subscription;
  private activeTasks = new Map<string, TaskJob>();
  private cancelledTasks = new Set<string>();

  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(TaskItem)
    private taskItemRepository: Repository<TaskItem>,
    @InjectRepository(OperationLog)
    private operationLogRepository: Repository<OperationLog>,
    private wikiService: WikiService,
    private tasksGateway: TasksGateway,
    private tokenStorage: TokenStorageService,
  ) {}

  onModuleInit() {
    this.logger.log('初始化任务管理器');
    this.initializeTaskStream();
  }

  onModuleDestroy() {
    this.logger.log('销毁任务管理器');
    if (this.taskSubscription) {
      this.taskSubscription.unsubscribe();
    }
    this.taskSubject.complete();
  }

  private initializeTaskStream() {
    this.taskSubscription = this.taskSubject.pipe(
      concatMap((job) => this.processTaskJob(job).pipe(
        delay(600), // 600ms延迟，符合飞书API每分钟100次的限制
        catchError(error => {
          this.logger.error(`任务 ${job.id} 处理失败:`, error);
          this.activeTasks.delete(job.id);
          return EMPTY;
        })
      ))
    ).subscribe({
      next: (result) => {
        this.logger.log('任务处理完成:', result);
      },
      error: (error) => {
        this.logger.error('任务流发生错误:', error);
      }
    });
  }

  addTask(taskData: { taskId: number }): string {
    const jobId = `task-${taskData.taskId}-${Date.now()}`;
    const job: TaskJob = {
      id: jobId,
      type: 'process-task',
      data: taskData,
      attempts: 0,
      maxAttempts: 3
    };

    this.activeTasks.set(jobId, job);
    this.taskSubject.next(job);
    this.logger.log(`任务 ${jobId} 已加入处理队列`);
    
    return jobId;
  }

  cancelTask(taskId: number): boolean {
    // 查找并取消相关的任务
    for (const [jobId, job] of this.activeTasks.entries()) {
      if (job.data.taskId === taskId) {
        this.cancelledTasks.add(jobId);
        this.activeTasks.delete(jobId);
        this.logger.log(`任务 ${jobId} (taskId: ${taskId}) 已取消`);
        return true;
      }
    }
    return false;
  }

  private processTaskJob(job: TaskJob) {
    return of(job).pipe(
      tap(() => this.logger.log(`开始处理任务 ${job.id}`)),
      concatMap(() => this.executeTask(job.data.taskId)),
      tap(() => {
        this.activeTasks.delete(job.id);
        this.logger.log(`任务 ${job.id} 处理完成`);
      }),
      catchError(error => {
        job.attempts++;
        if (job.attempts < job.maxAttempts) {
          this.logger.warn(`任务 ${job.id} 第 ${job.attempts} 次尝试失败，将重试:`, error);
          // 重新加入队列
          this.taskSubject.next(job);
          return EMPTY;
        } else {
          this.logger.error(`任务 ${job.id} 达到最大重试次数，标记为失败:`, error);
          this.updateTaskStatus(job.data.taskId, TaskStatus.FAILED);
          this.activeTasks.delete(job.id);
          throw error;
        }
      })
    );
  }

  private async executeTask(taskId: number): Promise<void> {
    // 检查任务是否被取消
    const jobId = Array.from(this.activeTasks.keys()).find(id => 
      this.activeTasks.get(id)?.data.taskId === taskId
    );
    
    if (jobId && this.cancelledTasks.has(jobId)) {
      this.logger.log(`任务 ${taskId} 已被取消，跳过处理`);
      return;
    }

    try {
      // 获取任务和任务项
      const task = await this.taskRepository.findOne({
        where: { id: taskId },
        relations: ['items'],
      });

      if (!task) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 获取当前操作用户的访问令牌
      const currentUserToken = this.tokenStorage.getUserToken(task.user_id);
      if (!currentUserToken) {
        throw new Error(`当前操作用户 ${task.user_id} 的访问令牌不存在`);
      }

      this.logger.log(`开始处理任务 ${taskId}，将文档转移给用户 ${task.target_user_id}`);

      // 获取当前用户信息
      const currentUserInfo = await this.wikiService.getCurrentUserInfo(currentUserToken);
      const currentUserName = currentUserInfo?.name || task.user_id;

      // 更新任务状态为运行中
      await this.updateTaskStatus(taskId, TaskStatus.RUNNING);
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: TaskStatus.RUNNING,
        completed_items: 0,
        failed_items: 0,
      });

      let completedItems = 0;
      let failedItems = 0;

      // 处理每个任务项
      for (const item of task.items) {
        // 再次检查是否被取消
        if (jobId && this.cancelledTasks.has(jobId)) {
          this.logger.log(`任务 ${taskId} 在处理过程中被取消`);
          await this.updateTaskStatus(taskId, TaskStatus.CANCELLED);
          return;
        }

        try {
          // 更新任务项状态为处理中
          await this.updateTaskItemStatus(item.id, TaskItemStatus.PROCESSING);

          // 记录开始转移日志
          await this.logOperation(
            taskId,
            currentUserName,
            item.wiki_title,
            task.target_user_name,
            OperationType.TRANSFER_START,
          );

          // 调用飞书API转移文档
          const result = await this.wikiService.moveWikiNode(
            currentUserToken,
            item.node_token,
            task.target_user_id,
            item.wiki_title,
            task.target_user_name,
          );

          if (result.code === 0) {
            await this.updateTaskItemStatus(item.id, TaskItemStatus.COMPLETED);
            await this.logOperation(
              taskId,
              currentUserName,
              item.wiki_title,
              task.target_user_name,
              OperationType.TRANSFER_SUCCESS,
            );
            completedItems++;
          } else {
            throw new Error(result.msg || '转移失败');
          }
        } catch (error) {
          this.logger.error(`处理任务项 ${item.id} 失败:`, error);
          await this.updateTaskItemStatus(item.id, TaskItemStatus.FAILED, error.message);
          await this.logOperation(
            taskId,
            currentUserName,
            item.wiki_title,
            task.target_user_name,
            OperationType.TRANSFER_FAILED,
          );
          failedItems++;
        }

        // 更新任务进度
        await this.updateTaskProgress(taskId, completedItems, failedItems);

        // 发送进度更新
        this.tasksGateway.sendTaskUpdate(task.user_id, {
          task_id: taskId,
          status: TaskStatus.RUNNING,
          completed_items: completedItems,
          failed_items: failedItems,
          current_item: {
            node_token: item.node_token,
            title: item.wiki_title,
            status: completedItems > failedItems ? TaskItemStatus.COMPLETED : TaskItemStatus.FAILED,
          },
        });
      }

      // 更新最终任务状态
      const finalStatus =
        failedItems === 0
          ? TaskStatus.COMPLETED
          : completedItems === 0
            ? TaskStatus.FAILED
            : TaskStatus.COMPLETED;

      await this.updateTaskStatus(taskId, finalStatus);

      // 发送最终状态更新
      this.tasksGateway.sendTaskUpdate(task.user_id, {
        task_id: taskId,
        status: finalStatus,
        completed_items: completedItems,
        failed_items: failedItems,
      });

      this.logger.log(`任务 ${taskId} 处理完成，成功: ${completedItems}, 失败: ${failedItems}`);
    } catch (error) {
      this.logger.error(`处理任务 ${taskId} 时发生错误:`, error);
      await this.updateTaskStatus(taskId, TaskStatus.FAILED);
      throw error;
    }
  }

  async updateTaskStatus(taskId: number, status: TaskStatus): Promise<void> {
    await this.taskRepository.update(taskId, { status });
  }

  async updateTaskItemStatus(itemId: number, status: TaskItemStatus, errorMessage?: string): Promise<void> {
    const updateData: any = { status };
    if (errorMessage) {
      updateData.error_message = errorMessage;
    }
    await this.taskItemRepository.update(itemId, updateData);
  }

  async updateTaskProgress(taskId: number, completedItems: number, failedItems: number): Promise<void> {
    await this.taskRepository.update(taskId, {
      completed_items: completedItems,
      failed_items: failedItems,
    });
  }

  async logOperation(
    taskId: number,
    userName: string,
    wikiTitle: string,
    targetUserName: string,
    operationType: OperationType,
    errorMessage?: string,
  ): Promise<void> {
    const log = this.operationLogRepository.create({
      task_id: taskId,
      user_name: userName,
      wiki_title: wikiTitle,
      target_user_name: targetUserName,
      operation_type: operationType,
    });

    await this.operationLogRepository.save(log);
  }
}
