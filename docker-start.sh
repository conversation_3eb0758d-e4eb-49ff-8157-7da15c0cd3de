#!/bin/bash

# 飞书知识库管理系统 Docker 启动脚本

echo "🐳 启动飞书知识库管理系统 Docker 容器..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p backend/data

# 设置目录权限
chmod 755 backend/data

# 停止并删除现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动容器
echo "🔨 构建并启动容器..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查后端健康状态
echo "🏥 检查后端服务健康状态..."
if curl -s http://localhost:3001/api/auth/app-id > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败，请检查日志"
    docker-compose logs backend
    exit 1
fi

# 检查前端健康状态
echo "🏥 检查前端服务健康状态..."
if curl -s http://localhost:3333 > /dev/null; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败，请检查日志"
    docker-compose logs frontend
    exit 1
fi

echo ""
echo "🎉 系统启动完成！"
echo "📱 前端地址: http://localhost:3333"
echo "🔧 后端地址: http://localhost:3001"
echo ""
echo "📋 常用命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  查看状态: docker-compose ps"
echo ""
echo "🔧 生产环境（使用Nginx）："
echo "  启动: docker-compose --profile production up -d"
echo "  访问: http://localhost"
