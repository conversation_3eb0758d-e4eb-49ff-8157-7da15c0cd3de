# 飞书知识库管理系统 - 前后端分离版本

## 📋 项目概述

这是一个重构后的飞书知识库管理系统，采用前后端分离架构，支持异步任务处理，避免了前端页面关闭导致任务中断的问题。

### 🏗️ 架构设计

- **前端**: Rsbuild + React + Ant Design (端口: 3000)
- **后端**: NestJS + TypeORM + SQLite + Bull Queue (端口: 3001)
- **数据库**: SQLite (本地文件数据库)
- **实时通信**: WebSocket (Socket.io)

### 📁 项目结构

```bash
feishu-wiki-manage/
├── frontend-rsbuild/  # Rsbuild前端应用
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── pages/         # 页面
│   │   ├── services/      # API服务层
│   │   └── hooks/         # React Hooks
│   ├── public/            # 静态资源
│   ├── rsbuild.config.ts  # Rsbuild配置
│   └── package.json
├── backend/           # NestJS后端服务
│   ├── src/
│   │   ├── modules/       # 功能模块
│   │   ├── entities/      # 数据库实体
│   │   ├── services/      # 任务管理服务
│   │   └── gateways/      # WebSocket网关
│   └── package.json
├── shared/            # 共享类型定义
│   └── types.ts
├── start-rsbuild.sh   # Rsbuild版本启动脚本
└── README.md
```

## 🚀 快速开始

### 1. 启动后端服务

```bash
cd backend
npm install
npm run start:dev
```

后端服务将在 http://localhost:3001 启动

### 2. 启动Rsbuild前端服务

```bash
cd frontend-rsbuild
npm install
npm run dev
```

前端服务将在 <http://localhost:3000> 启动

### 快速启动（推荐）

使用一键启动脚本：

```bash
./start-rsbuild.sh
```

### 3. 访问应用

打开浏览器访问 http://localhost:3000

## 🔧 主要功能

### 1. 文档转移任务创建
- 选择知识库空间
- 选择目标用户
- 选择要转移的文档
- 点击"创建转移任务"提交任务

### 2. 任务状态监控
- 实时查看任务执行进度
- WebSocket推送任务状态更新
- 支持任务取消（仅限等待中的任务）

### 3. 任务历史记录
- 查看所有任务列表
- 查看任务详细信息
- 查看每个文档的处理状态

## 📊 任务状态说明

- **等待中** (pending): 任务已创建，等待执行
- **执行中** (running): 任务正在执行
- **已完成** (completed): 任务执行完成
- **失败** (failed): 任务执行失败
- **已取消** (cancelled): 任务被用户取消

## 🔄 技术栈升级说明

### 前端技术栈升级：Next.js → Rsbuild + React

**升级原因：**
- 🚀 **更快的构建速度**：Rsbuild基于Rspack，构建速度比Next.js更快
- 🎯 **更简单的配置**：专注于构建工具，配置更简洁
- 📦 **更小的包体积**：优化的打包策略，生成更小的bundle
- 🔧 **更好的开发体验**：热更新更快，开发服务器启动更迅速

**保持不变：**
- ✅ React 18 + TypeScript
- ✅ Ant Design UI组件库
- ✅ 相同的页面结构和用户体验
- ✅ 所有现有功能完整保留

### 与原版本的主要区别

**原版本问题：**
- ❌ 前端直接执行转移任务
- ❌ 页面关闭会导致任务中断
- ❌ 无法追踪任务历史
- ❌ 错误处理不完善
- ❌ Next.js构建较慢

**新版本优势：**
- ✅ 后端异步任务处理
- ✅ 任务持久化存储
- ✅ 实时状态更新
- ✅ 完整的任务历史记录
- ✅ 更好的错误处理和重试机制
- ✅ Rsbuild快速构建和热更新

## 🛠️ 技术特性

### 后端特性
- **任务队列**: 使用Bull Queue处理异步任务
- **频率限制**: 每分钟100次API调用限制
- **数据持久化**: SQLite数据库存储任务信息
- **实时通信**: WebSocket推送任务状态
- **错误处理**: 完善的错误处理和日志记录

### 前端特性
- **实时更新**: WebSocket接收任务状态更新
- **响应式设计**: 支持不同屏幕尺寸
- **用户友好**: 直观的任务管理界面
- **状态管理**: 本地存储用户认证信息

## 📝 API接口

### 认证相关
- `GET /api/auth/app-id` - 获取应用ID
- `GET /api/auth/token` - 获取用户访问令牌

### 任务相关
- `POST /api/tasks` - 创建转移任务
- `GET /api/tasks` - 获取任务列表
- `GET /api/tasks/:id` - 获取任务详情
- `DELETE /api/tasks/:id` - 取消任务

### Wiki相关
- `GET /api/wiki/spaces` - 获取空间列表
- `GET /api/wiki/list` - 获取Wiki列表

### 用户相关
- `GET /api/users/me` - 获取当前用户信息
- `GET /api/users/search` - 搜索用户

## 🔒 安全说明

- 所有API接口都需要Bearer Token认证
- 用户只能访问自己创建的任务
- 飞书API密钥安全存储在后端

## 📈 性能优化

- 任务队列避免并发冲突
- 频率限制防止API超限
- WebSocket减少轮询开销
- SQLite提供快速本地存储

## 🐛 故障排除

### 后端启动失败
1. 检查端口3001是否被占用
2. 确认所有依赖已正确安装
3. 检查数据库文件权限

### 前端连接失败
1. 确认后端服务正常运行
2. 检查CORS配置
3. 确认WebSocket连接正常

### 任务执行失败
1. 检查飞书API访问权限
2. 确认用户令牌有效性
3. 查看后端日志获取详细错误信息

## 📞 支持

如有问题，请查看：
1. 浏览器开发者工具的控制台输出
2. 后端服务的日志输出
3. 数据库中的任务和日志记录

## 🎯 项目备份

原项目已备份到 `../feishu-wiki-manage-backup-*` 目录，可随时恢复。
