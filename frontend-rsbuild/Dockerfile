# 多阶段构建
# 第一阶段：构建应用
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码和配置文件
COPY . .

# 复制共享类型定义
COPY ../shared ./shared

# 构建应用
RUN npm run build

# 第二阶段：运行时环境
FROM node:18-alpine

# 安装serve用于提供静态文件服务
RUN npm install -g serve

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 复制共享类型定义（如果运行时需要）
COPY --from=builder /app/shared ./shared

# 创建启动脚本
RUN echo '#!/bin/sh\nserve -s dist -l 3000' > start.sh && chmod +x start.sh

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

# 启动应用
CMD ["./start.sh"]
